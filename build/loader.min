/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
"use strict";const _amdLoaderGlobal=this,_commonjsGlobal=typeof global=="object"?global:{};var AMDLoader;(function(d){d.global=_amdLoaderGlobal;class _{get isWindows(){return this._detect(),this._isWindows}get isNode(){return this._detect(),this._isNode}get isElectronRenderer(){return this._detect(),this._isElectronRenderer}get isWebWorker(){return this._detect(),this._isWebWorker}get isElectronNodeIntegrationWebWorker(){return this._detect(),this._isElectronNodeIntegrationWebWorker}constructor(){this._detected=!1,this._isWindows=!1,this._isNode=!1,this._isElectronRenderer=!1,this._isWebWorker=!1,this._isElectronNodeIntegrationWebWorker=!1}_detect(){this._detected||(this._detected=!0,this._isWindows=_._isWindows(),this._isNode=typeof module<"u"&&!!module.exports,this._isElectronRenderer=typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&process.type==="renderer",this._isWebWorker=typeof d.global.importScripts=="function",this._isElectronNodeIntegrationWebWorker=this._isWebWorker&&typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.electron<"u"&&process.type==="worker")}static _isWindows(){return typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.indexOf("Windows")>=0?!0:typeof process<"u"?process.platform==="win32":!1}}d.Environment=_})(AMDLoader||(AMDLoader={}));var AMDLoader;(function(d){class _{constructor(n,c,a){this.type=n,this.detail=c,this.timestamp=a}}d.LoaderEvent=_;class v{constructor(n){this._events=[new _(1,"",n)]}record(n,c){this._events.push(new _(n,c,d.Utilities.getHighPerformanceTimestamp()))}getEvents(){return this._events}}d.LoaderEventRecorder=v;class f{record(n,c){}getEvents(){return[]}}f.INSTANCE=new f,d.NullLoaderEventRecorder=f})(AMDLoader||(AMDLoader={}));var AMDLoader;(function(d){class _{static fileUriToFilePath(f,h){if(h=decodeURI(h).replace(/%23/g,"#"),f){if(/^file:\/\/\//.test(h))return h.substr(8);if(/^file:\/\//.test(h))return h.substr(5)}else if(/^file:\/\//.test(h))return h.substr(7);return h}static startsWith(f,h){return f.length>=h.length&&f.substr(0,h.length)===h}static endsWith(f,h){return f.length>=h.length&&f.substr(f.length-h.length)===h}static containsQueryString(f){return/^[^\#]*\?/gi.test(f)}static isAbsolutePath(f){return/^((http:\/\/)|(https:\/\/)|(file:\/\/)|(\/))/.test(f)}static forEachProperty(f,h){if(f){let n;for(n in f)f.hasOwnProperty(n)&&h(n,f[n])}}static isEmpty(f){let h=!0;return _.forEachProperty(f,()=>{h=!1}),h}static recursiveClone(f){if(!f||typeof f!="object"||f instanceof RegExp||!Array.isArray(f)&&Object.getPrototypeOf(f)!==Object.prototype)return f;let h=Array.isArray(f)?[]:{};return _.forEachProperty(f,(n,c)=>{c&&typeof c=="object"?h[n]=_.recursiveClone(c):h[n]=c}),h}static generateAnonymousModule(){return"===anonymous"+_.NEXT_ANONYMOUS_ID+++"==="}static isAnonymousModule(f){return _.startsWith(f,"===anonymous")}static getHighPerformanceTimestamp(){return this.PERFORMANCE_NOW_PROBED||(this.PERFORMANCE_NOW_PROBED=!0,this.HAS_PERFORMANCE_NOW=d.global.performance&&typeof d.global.performance.now=="function"),this.HAS_PERFORMANCE_NOW?d.global.performance.now():Date.now()}}_.NEXT_ANONYMOUS_ID=1,_.PERFORMANCE_NOW_PROBED=!1,_.HAS_PERFORMANCE_NOW=!1,d.Utilities=_})(AMDLoader||(AMDLoader={}));var AMDLoader;(function(d){function _(h){if(h instanceof Error)return h;const n=new Error(h.message||String(h)||"Unknown Error");return h.stack&&(n.stack=h.stack),n}d.ensureError=_;class v{static validateConfigurationOptions(n){function c(a){if(a.phase==="loading"){console.error('Loading "'+a.moduleId+'" failed'),console.error(a),console.error("Here are the modules that depend on it:"),console.error(a.neededBy);return}if(a.phase==="factory"){console.error('The factory function of "'+a.moduleId+'" has thrown an exception'),console.error(a),console.error("Here are the modules that depend on it:"),console.error(a.neededBy);return}}if(n=n||{},typeof n.baseUrl!="string"&&(n.baseUrl=""),typeof n.isBuild!="boolean"&&(n.isBuild=!1),typeof n.paths!="object"&&(n.paths={}),typeof n.config!="object"&&(n.config={}),typeof n.catchError>"u"&&(n.catchError=!1),typeof n.recordStats>"u"&&(n.recordStats=!1),typeof n.urlArgs!="string"&&(n.urlArgs=""),typeof n.onError!="function"&&(n.onError=c),Array.isArray(n.ignoreDuplicateModules)||(n.ignoreDuplicateModules=[]),n.baseUrl.length>0&&(d.Utilities.endsWith(n.baseUrl,"/")||(n.baseUrl+="/")),typeof n.cspNonce!="string"&&(n.cspNonce=""),typeof n.preferScriptTags>"u"&&(n.preferScriptTags=!1),n.nodeCachedData&&typeof n.nodeCachedData=="object"&&(typeof n.nodeCachedData.seed!="string"&&(n.nodeCachedData.seed="seed"),(typeof n.nodeCachedData.writeDelay!="number"||n.nodeCachedData.writeDelay<0)&&(n.nodeCachedData.writeDelay=1e3*7),!n.nodeCachedData.path||typeof n.nodeCachedData.path!="string")){const a=_(new Error("INVALID cached data configuration, 'path' MUST be set"));a.phase="configuration",n.onError(a),n.nodeCachedData=void 0}return n}static mergeConfigurationOptions(n=null,c=null){let a=d.Utilities.recursiveClone(c||{});return d.Utilities.forEachProperty(n,(t,e)=>{t==="ignoreDuplicateModules"&&typeof a.ignoreDuplicateModules<"u"?a.ignoreDuplicateModules=a.ignoreDuplicateModules.concat(e):t==="paths"&&typeof a.paths<"u"?d.Utilities.forEachProperty(e,(i,s)=>a.paths[i]=s):t==="config"&&typeof a.config<"u"?d.Utilities.forEachProperty(e,(i,s)=>a.config[i]=s):a[t]=d.Utilities.recursiveClone(e)}),v.validateConfigurationOptions(a)}}d.ConfigurationOptionsUtil=v;class f{constructor(n,c){if(this._env=n,this.options=v.mergeConfigurationOptions(c),this._createIgnoreDuplicateModulesMap(),this._createSortedPathsRules(),this.options.baseUrl===""&&this.options.nodeRequire&&this.options.nodeRequire.main&&this.options.nodeRequire.main.filename&&this._env.isNode){let a=this.options.nodeRequire.main.filename,t=Math.max(a.lastIndexOf("/"),a.lastIndexOf("\\"));this.options.baseUrl=a.substring(0,t+1)}}_createIgnoreDuplicateModulesMap(){this.ignoreDuplicateModulesMap={};for(let n=0;n<this.options.ignoreDuplicateModules.length;n++)this.ignoreDuplicateModulesMap[this.options.ignoreDuplicateModules[n]]=!0}_createSortedPathsRules(){this.sortedPathsRules=[],d.Utilities.forEachProperty(this.options.paths,(n,c)=>{Array.isArray(c)?this.sortedPathsRules.push({from:n,to:c}):this.sortedPathsRules.push({from:n,to:[c]})}),this.sortedPathsRules.sort((n,c)=>c.from.length-n.from.length)}cloneAndMerge(n){return new f(this._env,v.mergeConfigurationOptions(n,this.options))}getOptionsLiteral(){return this.options}_applyPaths(n){let c;for(let a=0,t=this.sortedPathsRules.length;a<t;a++)if(c=this.sortedPathsRules[a],d.Utilities.startsWith(n,c.from)){let e=[];for(let i=0,s=c.to.length;i<s;i++)e.push(c.to[i]+n.substr(c.from.length));return e}return[n]}_addUrlArgsToUrl(n){return d.Utilities.containsQueryString(n)?n+"&"+this.options.urlArgs:n+"?"+this.options.urlArgs}_addUrlArgsIfNecessaryToUrl(n){return this.options.urlArgs?this._addUrlArgsToUrl(n):n}_addUrlArgsIfNecessaryToUrls(n){if(this.options.urlArgs)for(let c=0,a=n.length;c<a;c++)n[c]=this._addUrlArgsToUrl(n[c]);return n}moduleIdToPaths(n){if(this._env.isNode&&this.options.amdModulesPattern instanceof RegExp&&!this.options.amdModulesPattern.test(n))return this.isBuild()?["empty:"]:["node|"+n];let c=n,a;if(!d.Utilities.endsWith(c,".js")&&!d.Utilities.isAbsolutePath(c)){a=this._applyPaths(c);for(let t=0,e=a.length;t<e;t++)this.isBuild()&&a[t]==="empty:"||(d.Utilities.isAbsolutePath(a[t])||(a[t]=this.options.baseUrl+a[t]),!d.Utilities.endsWith(a[t],".js")&&!d.Utilities.containsQueryString(a[t])&&(a[t]=a[t]+".js"))}else!d.Utilities.endsWith(c,".js")&&!d.Utilities.containsQueryString(c)&&(c=c+".js"),a=[c];return this._addUrlArgsIfNecessaryToUrls(a)}requireToUrl(n){let c=n;return d.Utilities.isAbsolutePath(c)||(c=this._applyPaths(c)[0],d.Utilities.isAbsolutePath(c)||(c=this.options.baseUrl+c)),this._addUrlArgsIfNecessaryToUrl(c)}isBuild(){return this.options.isBuild}shouldInvokeFactory(n){return!!(!this.options.isBuild||d.Utilities.isAnonymousModule(n)||this.options.buildForceInvokeFactory&&this.options.buildForceInvokeFactory[n])}isDuplicateMessageIgnoredFor(n){return this.ignoreDuplicateModulesMap.hasOwnProperty(n)}getConfigForModule(n){if(this.options.config)return this.options.config[n]}shouldCatchError(){return this.options.catchError}shouldRecordStats(){return this.options.recordStats}onError(n){this.options.onError(n)}}d.Configuration=f})(AMDLoader||(AMDLoader={}));var AMDLoader;(function(d){class _{constructor(e){this._env=e,this._scriptLoader=null,this._callbackMap={}}load(e,i,s,r){if(!this._scriptLoader)if(this._env.isWebWorker)this._scriptLoader=new h;else if(this._env.isElectronRenderer){const{preferScriptTags:u}=e.getConfig().getOptionsLiteral();u?this._scriptLoader=new v:this._scriptLoader=new n(this._env)}else this._env.isNode?this._scriptLoader=new n(this._env):this._scriptLoader=new v;let o={callback:s,errorback:r};if(this._callbackMap.hasOwnProperty(i)){this._callbackMap[i].push(o);return}this._callbackMap[i]=[o],this._scriptLoader.load(e,i,()=>this.triggerCallback(i),u=>this.triggerErrorback(i,u))}triggerCallback(e){let i=this._callbackMap[e];delete this._callbackMap[e];for(let s=0;s<i.length;s++)i[s].callback()}triggerErrorback(e,i){let s=this._callbackMap[e];delete this._callbackMap[e];for(let r=0;r<s.length;r++)s[r].errorback(i)}}class v{attachListeners(e,i,s){let r=()=>{e.removeEventListener("load",o),e.removeEventListener("error",u)},o=l=>{r(),i()},u=l=>{r(),s(l)};e.addEventListener("load",o),e.addEventListener("error",u)}load(e,i,s,r){if(/^node\|/.test(i)){let o=e.getConfig().getOptionsLiteral(),u=c(e.getRecorder(),o.nodeRequire||d.global.nodeRequire),l=i.split("|"),g=null;try{g=u(l[1])}catch(p){r(p);return}e.enqueueDefineAnonymousModule([],()=>g),s()}else{let o=document.createElement("script");o.setAttribute("async","async"),o.setAttribute("type","text/javascript"),this.attachListeners(o,s,r);const{trustedTypesPolicy:u}=e.getConfig().getOptionsLiteral();u&&(i=u.createScriptURL(i)),o.setAttribute("src",i);const{cspNonce:l}=e.getConfig().getOptionsLiteral();l&&o.setAttribute("nonce",l),document.getElementsByTagName("head")[0].appendChild(o)}}}function f(t){const{trustedTypesPolicy:e}=t.getConfig().getOptionsLiteral();try{return(e?self.eval(e.createScript("","true")):new Function("true")).call(self),!0}catch{return!1}}class h{constructor(){this._cachedCanUseEval=null}_canUseEval(e){return this._cachedCanUseEval===null&&(this._cachedCanUseEval=f(e)),this._cachedCanUseEval}load(e,i,s,r){if(/^node\|/.test(i)){const o=e.getConfig().getOptionsLiteral(),u=c(e.getRecorder(),o.nodeRequire||d.global.nodeRequire),l=i.split("|");let g=null;try{g=u(l[1])}catch(p){r(p);return}e.enqueueDefineAnonymousModule([],function(){return g}),s()}else{const{trustedTypesPolicy:o}=e.getConfig().getOptionsLiteral();if(!(/^((http:)|(https:)|(file:))/.test(i)&&i.substring(0,self.origin.length)!==self.origin)&&this._canUseEval(e)){fetch(i).then(l=>{if(l.status!==200)throw new Error(l.statusText);return l.text()}).then(l=>{l=`${l}
//# sourceURL=${i}`,(o?self.eval(o.createScript("",l)):new Function(l)).call(self),s()}).then(void 0,r);return}try{o&&(i=o.createScriptURL(i)),importScripts(i),s()}catch(l){r(l)}}}}class n{constructor(e){this._env=e,this._didInitialize=!1,this._didPatchNodeRequire=!1}_init(e){this._didInitialize||(this._didInitialize=!0,this._fs=e("fs"),this._vm=e("vm"),this._path=e("path"),this._crypto=e("crypto"))}_initNodeRequire(e,i){const{nodeCachedData:s}=i.getConfig().getOptionsLiteral();if(!s||this._didPatchNodeRequire)return;this._didPatchNodeRequire=!0;const r=this,o=e("module");function u(l){const g=l.constructor;let p=function(y){try{return l.require(y)}finally{}};return p.resolve=function(y,E){return g._resolveFilename(y,l,!1,E)},p.resolve.paths=function(y){return g._resolveLookupPaths(y,l)},p.main=process.mainModule,p.extensions=g._extensions,p.cache=g._cache,p}o.prototype._compile=function(l,g){const p=o.wrap(l.replace(/^#!.*/,"")),m=i.getRecorder(),y=r._getCachedDataPath(s,g),E={filename:g};let R;try{const x=r._fs.readFileSync(y);R=x.slice(0,16),E.cachedData=x.slice(16),m.record(60,y)}catch{m.record(61,y)}const C=new r._vm.Script(p,E),I=C.runInThisContext(E),w=r._path.dirname(g),b=u(this),U=[this.exports,b,this,g,w,process,_commonjsGlobal,Buffer],P=I.apply(this.exports,U);return r._handleCachedData(C,p,y,!E.cachedData,i),r._verifyCachedData(C,p,y,R,i),P}}load(e,i,s,r){const o=e.getConfig().getOptionsLiteral(),u=c(e.getRecorder(),o.nodeRequire||d.global.nodeRequire),l=o.nodeInstrumenter||function(p){return p};this._init(u),this._initNodeRequire(u,e);let g=e.getRecorder();if(/^node\|/.test(i)){let p=i.split("|"),m=null;try{m=u(p[1])}catch(y){r(y);return}e.enqueueDefineAnonymousModule([],()=>m),s()}else{i=d.Utilities.fileUriToFilePath(this._env.isWindows,i);const p=this._path.normalize(i),m=this._getElectronRendererScriptPathOrUri(p),y=!!o.nodeCachedData,E=y?this._getCachedDataPath(o.nodeCachedData,i):void 0;this._readSourceAndCachedData(p,E,g,(R,C,I,w)=>{if(R){r(R);return}let b;C.charCodeAt(0)===n._BOM?b=n._PREFIX+C.substring(1)+n._SUFFIX:b=n._PREFIX+C+n._SUFFIX,b=l(b,p);const U={filename:m,cachedData:I},P=this._createAndEvalScript(e,b,U,s,r);this._handleCachedData(P,b,E,y&&!I,e),this._verifyCachedData(P,b,E,w,e)})}}_createAndEvalScript(e,i,s,r,o){const u=e.getRecorder();u.record(31,s.filename);const l=new this._vm.Script(i,s),g=l.runInThisContext(s),p=e.getGlobalAMDDefineFunc();let m=!1;const y=function(){return m=!0,p.apply(null,arguments)};return y.amd=p.amd,g.call(d.global,e.getGlobalAMDRequireFunc(),y,s.filename,this._path.dirname(s.filename)),u.record(32,s.filename),m?r():o(new Error(`Didn't receive define call in ${s.filename}!`)),l}_getElectronRendererScriptPathOrUri(e){if(!this._env.isElectronRenderer)return e;let i=e.match(/^([a-z])\:(.*)/i);return i?`file:///${(i[1].toUpperCase()+":"+i[2]).replace(/\\/g,"/")}`:`file://${e}`}_getCachedDataPath(e,i){const s=this._crypto.createHash("md5").update(i,"utf8").update(e.seed,"utf8").update(process.arch,"").digest("hex"),r=this._path.basename(i).replace(/\.js$/,"");return this._path.join(e.path,`${r}-${s}.code`)}_handleCachedData(e,i,s,r,o){e.cachedDataRejected?this._fs.unlink(s,u=>{o.getRecorder().record(62,s),this._createAndWriteCachedData(e,i,s,o),u&&o.getConfig().onError(u)}):r&&this._createAndWriteCachedData(e,i,s,o)}_createAndWriteCachedData(e,i,s,r){let o=Math.ceil(r.getConfig().getOptionsLiteral().nodeCachedData.writeDelay*(1+Math.random())),u=-1,l=0,g;const p=()=>{setTimeout(()=>{g||(g=this._crypto.createHash("md5").update(i,"utf8").digest());const m=e.createCachedData();if(!(m.length===0||m.length===u||l>=5)){if(m.length<u){p();return}u=m.length,this._fs.writeFile(s,Buffer.concat([g,m]),y=>{y&&r.getConfig().onError(y),r.getRecorder().record(63,s),p()})}},o*Math.pow(4,l++))};p()}_readSourceAndCachedData(e,i,s,r){if(!i)this._fs.readFile(e,{encoding:"utf8"},r);else{let o,u,l,g=2;const p=m=>{m?r(m):--g===0&&r(void 0,o,u,l)};this._fs.readFile(e,{encoding:"utf8"},(m,y)=>{o=y,p(m)}),this._fs.readFile(i,(m,y)=>{!m&&y&&y.length>0?(l=y.slice(0,16),u=y.slice(16),s.record(60,i)):s.record(61,i),p()})}}_verifyCachedData(e,i,s,r,o){r&&(e.cachedDataRejected||setTimeout(()=>{const u=this._crypto.createHash("md5").update(i,"utf8").digest();r.equals(u)||(o.getConfig().onError(new Error(`FAILED TO VERIFY CACHED DATA, deleting stale '${s}' now, but a RESTART IS REQUIRED`)),this._fs.unlink(s,l=>{l&&o.getConfig().onError(l)}))},Math.ceil(5e3*(1+Math.random()))))}}n._BOM=65279,n._PREFIX="(function (require, define, __filename, __dirname) { ",n._SUFFIX=`
});`;function c(t,e){if(e.__$__isRecorded)return e;const i=function(r){t.record(33,r);try{return e(r)}finally{t.record(34,r)}};return i.__$__isRecorded=!0,i}d.ensureRecordedNodeRequire=c;function a(t){return new _(t)}d.createScriptLoader=a})(AMDLoader||(AMDLoader={}));var AMDLoader;(function(d){class _{constructor(t){let e=t.lastIndexOf("/");e!==-1?this.fromModulePath=t.substr(0,e+1):this.fromModulePath=""}static _normalizeModuleId(t){let e=t,i;for(i=/\/\.\//;i.test(e);)e=e.replace(i,"/");for(e=e.replace(/^\.\//g,""),i=/\/(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//;i.test(e);)e=e.replace(i,"/");return e=e.replace(/^(([^\/])|([^\/][^\/\.])|([^\/\.][^\/])|([^\/][^\/][^\/]+))\/\.\.\//,""),e}resolveModule(t){let e=t;return d.Utilities.isAbsolutePath(e)||(d.Utilities.startsWith(e,"./")||d.Utilities.startsWith(e,"../"))&&(e=_._normalizeModuleId(this.fromModulePath+e)),e}}_.ROOT=new _(""),d.ModuleIdResolver=_;class v{constructor(t,e,i,s,r,o){this.id=t,this.strId=e,this.dependencies=i,this._callback=s,this._errorback=r,this.moduleIdResolver=o,this.exports={},this.error=null,this.exportsPassedIn=!1,this.unresolvedDependenciesCount=this.dependencies.length,this._isComplete=!1}static _safeInvokeFunction(t,e){try{return{returnedValue:t.apply(d.global,e),producedError:null}}catch(i){return{returnedValue:null,producedError:i}}}static _invokeFactory(t,e,i,s){return t.shouldInvokeFactory(e)?t.shouldCatchError()?this._safeInvokeFunction(i,s):{returnedValue:i.apply(d.global,s),producedError:null}:{returnedValue:null,producedError:null}}complete(t,e,i,s){this._isComplete=!0;let r=null;if(this._callback)if(typeof this._callback=="function"){t.record(21,this.strId);let o=v._invokeFactory(e,this.strId,this._callback,i);r=o.producedError,t.record(22,this.strId),!r&&typeof o.returnedValue<"u"&&(!this.exportsPassedIn||d.Utilities.isEmpty(this.exports))&&(this.exports=o.returnedValue)}else this.exports=this._callback;if(r){let o=d.ensureError(r);o.phase="factory",o.moduleId=this.strId,o.neededBy=s(this.id),this.error=o,e.onError(o)}this.dependencies=null,this._callback=null,this._errorback=null,this.moduleIdResolver=null}onDependencyError(t){return this._isComplete=!0,this.error=t,this._errorback?(this._errorback(t),!0):!1}isComplete(){return this._isComplete}}d.Module=v;class f{constructor(){this._nextId=0,this._strModuleIdToIntModuleId=new Map,this._intModuleIdToStrModuleId=[],this.getModuleId("exports"),this.getModuleId("module"),this.getModuleId("require")}getMaxModuleId(){return this._nextId}getModuleId(t){let e=this._strModuleIdToIntModuleId.get(t);return typeof e>"u"&&(e=this._nextId++,this._strModuleIdToIntModuleId.set(t,e),this._intModuleIdToStrModuleId[e]=t),e}getStrModuleId(t){return this._intModuleIdToStrModuleId[t]}}class h{constructor(t){this.id=t}}h.EXPORTS=new h(0),h.MODULE=new h(1),h.REQUIRE=new h(2),d.RegularDependency=h;class n{constructor(t,e,i){this.id=t,this.pluginId=e,this.pluginParam=i}}d.PluginDependency=n;class c{constructor(t,e,i,s,r=0){this._env=t,this._scriptLoader=e,this._loaderAvailableTimestamp=r,this._defineFunc=i,this._requireFunc=s,this._moduleIdProvider=new f,this._config=new d.Configuration(this._env),this._hasDependencyCycle=!1,this._modules2=[],this._knownModules2=[],this._inverseDependencies2=[],this._inversePluginDependencies2=new Map,this._currentAnonymousDefineCall=null,this._recorder=null,this._buildInfoPath=[],this._buildInfoDefineStack=[],this._buildInfoDependencies=[],this._requireFunc.moduleManager=this}reset(){return new c(this._env,this._scriptLoader,this._defineFunc,this._requireFunc,this._loaderAvailableTimestamp)}getGlobalAMDDefineFunc(){return this._defineFunc}getGlobalAMDRequireFunc(){return this._requireFunc}static _findRelevantLocationInStack(t,e){let i=o=>o.replace(/\\/g,"/"),s=i(t),r=e.split(/\n/);for(let o=0;o<r.length;o++){let u=r[o].match(/(.*):(\d+):(\d+)\)?$/);if(u){let l=u[1],g=u[2],p=u[3],m=Math.max(l.lastIndexOf(" ")+1,l.lastIndexOf("(")+1);if(l=l.substr(m),l=i(l),l===s){let y={line:parseInt(g,10),col:parseInt(p,10)};return y.line===1&&(y.col-=53),y}}}throw new Error("Could not correlate define call site for needle "+t)}getBuildInfo(){if(!this._config.isBuild())return null;let t=[],e=0;for(let i=0,s=this._modules2.length;i<s;i++){let r=this._modules2[i];if(!r)continue;let o=this._buildInfoPath[r.id]||null,u=this._buildInfoDefineStack[r.id]||null,l=this._buildInfoDependencies[r.id];t[e++]={id:r.strId,path:o,defineLocation:o&&u?c._findRelevantLocationInStack(o,u):null,dependencies:l,shim:null,exports:r.exports}}return t}getRecorder(){return this._recorder||(this._config.shouldRecordStats()?this._recorder=new d.LoaderEventRecorder(this._loaderAvailableTimestamp):this._recorder=d.NullLoaderEventRecorder.INSTANCE),this._recorder}getLoaderEvents(){return this.getRecorder().getEvents()}enqueueDefineAnonymousModule(t,e){if(this._currentAnonymousDefineCall!==null)throw new Error("Can only have one anonymous define call per script file");let i=null;this._config.isBuild()&&(i=new Error("StackLocation").stack||null),this._currentAnonymousDefineCall={stack:i,dependencies:t,callback:e}}defineModule(t,e,i,s,r,o=new _(t)){let u=this._moduleIdProvider.getModuleId(t);if(this._modules2[u]){this._config.isDuplicateMessageIgnoredFor(t)||console.warn("Duplicate definition of module '"+t+"'");return}let l=new v(u,t,this._normalizeDependencies(e,o),i,s,o);this._modules2[u]=l,this._config.isBuild()&&(this._buildInfoDefineStack[u]=r,this._buildInfoDependencies[u]=(l.dependencies||[]).map(g=>this._moduleIdProvider.getStrModuleId(g.id))),this._resolve(l)}_normalizeDependency(t,e){if(t==="exports")return h.EXPORTS;if(t==="module")return h.MODULE;if(t==="require")return h.REQUIRE;let i=t.indexOf("!");if(i>=0){let s=e.resolveModule(t.substr(0,i)),r=e.resolveModule(t.substr(i+1)),o=this._moduleIdProvider.getModuleId(s+"!"+r),u=this._moduleIdProvider.getModuleId(s);return new n(o,u,r)}return new h(this._moduleIdProvider.getModuleId(e.resolveModule(t)))}_normalizeDependencies(t,e){let i=[],s=0;for(let r=0,o=t.length;r<o;r++)i[s++]=this._normalizeDependency(t[r],e);return i}_relativeRequire(t,e,i,s){if(typeof e=="string")return this.synchronousRequire(e,t);this.defineModule(d.Utilities.generateAnonymousModule(),e,i,s,null,t)}synchronousRequire(t,e=new _(t)){let i=this._normalizeDependency(t,e),s=this._modules2[i.id];if(!s)throw new Error("Check dependency list! Synchronous require cannot resolve module '"+t+"'. This is the first mention of this module!");if(!s.isComplete())throw new Error("Check dependency list! Synchronous require cannot resolve module '"+t+"'. This module has not been resolved completely yet.");if(s.error)throw s.error;return s.exports}configure(t,e){let i=this._config.shouldRecordStats();e?this._config=new d.Configuration(this._env,t):this._config=this._config.cloneAndMerge(t),this._config.shouldRecordStats()&&!i&&(this._recorder=null)}getConfig(){return this._config}_onLoad(t){if(this._currentAnonymousDefineCall!==null){let e=this._currentAnonymousDefineCall;this._currentAnonymousDefineCall=null,this.defineModule(this._moduleIdProvider.getStrModuleId(t),e.dependencies,e.callback,null,e.stack)}}_createLoadError(t,e){let i=this._moduleIdProvider.getStrModuleId(t),s=(this._inverseDependencies2[t]||[]).map(o=>this._moduleIdProvider.getStrModuleId(o));const r=d.ensureError(e);return r.phase="loading",r.moduleId=i,r.neededBy=s,r}_onLoadError(t,e){const i=this._createLoadError(t,e);this._modules2[t]||(this._modules2[t]=new v(t,this._moduleIdProvider.getStrModuleId(t),[],()=>{},null,null));let s=[];for(let u=0,l=this._moduleIdProvider.getMaxModuleId();u<l;u++)s[u]=!1;let r=!1,o=[];for(o.push(t),s[t]=!0;o.length>0;){let u=o.shift(),l=this._modules2[u];l&&(r=l.onDependencyError(i)||r);let g=this._inverseDependencies2[u];if(g)for(let p=0,m=g.length;p<m;p++){let y=g[p];s[y]||(o.push(y),s[y]=!0)}}r||this._config.onError(i)}_hasDependencyPath(t,e){let i=this._modules2[t];if(!i)return!1;let s=[];for(let o=0,u=this._moduleIdProvider.getMaxModuleId();o<u;o++)s[o]=!1;let r=[];for(r.push(i),s[t]=!0;r.length>0;){let u=r.shift().dependencies;if(u)for(let l=0,g=u.length;l<g;l++){let p=u[l];if(p.id===e)return!0;let m=this._modules2[p.id];m&&!s[p.id]&&(s[p.id]=!0,r.push(m))}}return!1}_findCyclePath(t,e,i){if(t===e||i===50)return[t];let s=this._modules2[t];if(!s)return null;let r=s.dependencies;if(r)for(let o=0,u=r.length;o<u;o++){let l=this._findCyclePath(r[o].id,e,i+1);if(l!==null)return l.push(t),l}return null}_createRequire(t){let e=(i,s,r)=>this._relativeRequire(t,i,s,r);return e.toUrl=i=>this._config.requireToUrl(t.resolveModule(i)),e.getStats=()=>this.getLoaderEvents(),e.hasDependencyCycle=()=>this._hasDependencyCycle,e.config=(i,s=!1)=>{this.configure(i,s)},e.__$__nodeRequire=d.global.nodeRequire,e}_loadModule(t){if(this._modules2[t]||this._knownModules2[t])return;this._knownModules2[t]=!0;let e=this._moduleIdProvider.getStrModuleId(t),i=this._config.moduleIdToPaths(e),s=/^@[^\/]+\/[^\/]+$/;this._env.isNode&&(e.indexOf("/")===-1||s.test(e))&&i.push("node|"+e);let r=-1,o=u=>{if(r++,r>=i.length)this._onLoadError(t,u);else{let l=i[r],g=this.getRecorder();if(this._config.isBuild()&&l==="empty:"){this._buildInfoPath[t]=l,this.defineModule(this._moduleIdProvider.getStrModuleId(t),[],null,null,null),this._onLoad(t);return}g.record(10,l),this._scriptLoader.load(this,l,()=>{this._config.isBuild()&&(this._buildInfoPath[t]=l),g.record(11,l),this._onLoad(t)},p=>{g.record(12,l),o(p)})}};o(null)}_loadPluginDependency(t,e){if(this._modules2[e.id]||this._knownModules2[e.id])return;this._knownModules2[e.id]=!0;let i=s=>{this.defineModule(this._moduleIdProvider.getStrModuleId(e.id),[],s,null,null)};i.error=s=>{this._config.onError(this._createLoadError(e.id,s))},t.load(e.pluginParam,this._createRequire(_.ROOT),i,this._config.getOptionsLiteral())}_resolve(t){let e=t.dependencies;if(e)for(let i=0,s=e.length;i<s;i++){let r=e[i];if(r===h.EXPORTS){t.exportsPassedIn=!0,t.unresolvedDependenciesCount--;continue}if(r===h.MODULE){t.unresolvedDependenciesCount--;continue}if(r===h.REQUIRE){t.unresolvedDependenciesCount--;continue}let o=this._modules2[r.id];if(o&&o.isComplete()){if(o.error){t.onDependencyError(o.error);return}t.unresolvedDependenciesCount--;continue}if(this._hasDependencyPath(r.id,t.id)){this._hasDependencyCycle=!0,console.warn("There is a dependency cycle between '"+this._moduleIdProvider.getStrModuleId(r.id)+"' and '"+this._moduleIdProvider.getStrModuleId(t.id)+"'. The cyclic path follows:");let u=this._findCyclePath(r.id,t.id,0)||[];u.reverse(),u.push(r.id),console.warn(u.map(l=>this._moduleIdProvider.getStrModuleId(l)).join(` =>
`)),t.unresolvedDependenciesCount--;continue}if(this._inverseDependencies2[r.id]=this._inverseDependencies2[r.id]||[],this._inverseDependencies2[r.id].push(t.id),r instanceof n){let u=this._modules2[r.pluginId];if(u&&u.isComplete()){this._loadPluginDependency(u.exports,r);continue}let l=this._inversePluginDependencies2.get(r.pluginId);l||(l=[],this._inversePluginDependencies2.set(r.pluginId,l)),l.push(r),this._loadModule(r.pluginId);continue}this._loadModule(r.id)}t.unresolvedDependenciesCount===0&&this._onModuleComplete(t)}_onModuleComplete(t){let e=this.getRecorder();if(t.isComplete())return;let i=t.dependencies,s=[];if(i)for(let l=0,g=i.length;l<g;l++){let p=i[l];if(p===h.EXPORTS){s[l]=t.exports;continue}if(p===h.MODULE){s[l]={id:t.strId,config:()=>this._config.getConfigForModule(t.strId)};continue}if(p===h.REQUIRE){s[l]=this._createRequire(t.moduleIdResolver);continue}let m=this._modules2[p.id];if(m){s[l]=m.exports;continue}s[l]=null}const r=l=>(this._inverseDependencies2[l]||[]).map(g=>this._moduleIdProvider.getStrModuleId(g));t.complete(e,this._config,s,r);let o=this._inverseDependencies2[t.id];if(this._inverseDependencies2[t.id]=null,o)for(let l=0,g=o.length;l<g;l++){let p=o[l],m=this._modules2[p];m.unresolvedDependenciesCount--,m.unresolvedDependenciesCount===0&&this._onModuleComplete(m)}let u=this._inversePluginDependencies2.get(t.id);if(u){this._inversePluginDependencies2.delete(t.id);for(let l=0,g=u.length;l<g;l++)this._loadPluginDependency(t.exports,u[l])}}}d.ModuleManager=c})(AMDLoader||(AMDLoader={}));var define,AMDLoader;(function(d){const _=new d.Environment;let v=null;const f=function(a,t,e){typeof a!="string"&&(e=t,t=a,a=null),(typeof t!="object"||!Array.isArray(t))&&(e=t,t=null),t||(t=["require","exports","module"]),a?v.defineModule(a,t,e,null,null):v.enqueueDefineAnonymousModule(t,e)};f.amd={jQuery:!0};const h=function(a,t=!1){v.configure(a,t)},n=function(){if(arguments.length===1){if(arguments[0]instanceof Object&&!Array.isArray(arguments[0])){h(arguments[0]);return}if(typeof arguments[0]=="string")return v.synchronousRequire(arguments[0])}if((arguments.length===2||arguments.length===3)&&Array.isArray(arguments[0])){v.defineModule(d.Utilities.generateAnonymousModule(),arguments[0],arguments[1],arguments[2],null);return}throw new Error("Unrecognized require call")};n.config=h,n.getConfig=function(){return v.getConfig().getOptionsLiteral()},n.reset=function(){v=v.reset()},n.getBuildInfo=function(){return v.getBuildInfo()},n.getStats=function(){return v.getLoaderEvents()},n.define=f;function c(){if(typeof d.global.require<"u"||typeof require<"u"){const a=d.global.require||require;if(typeof a=="function"&&typeof a.resolve=="function"){const t=d.ensureRecordedNodeRequire(v.getRecorder(),a);d.global.nodeRequire=t,n.nodeRequire=t,n.__$__nodeRequire=t}}_.isNode&&!_.isElectronRenderer&&!_.isElectronNodeIntegrationWebWorker?module.exports=n:(_.isElectronRenderer||(d.global.define=f),d.global.require=n)}d.init=c,(typeof d.global.define!="function"||!d.global.define.amd)&&(v=new d.ModuleManager(_,d.createScriptLoader(_),f,n,d.Utilities.getHighPerformanceTimestamp()),typeof d.global.require<"u"&&typeof d.global.require!="function"&&n.config(d.global.require),define=function(){return f.apply(null,arguments)},define.amd=f.amd,typeof doNotInitLoader>"u"&&c())})(AMDLoader||(AMDLoader={})),define("vs/css",["require","exports"],function(d,_){"use strict";Object.defineProperty(_,"__esModule",{value:!0}),_.load=v;function v(a,t,e,i){if(i=i||{},(i["vs/css"]||{}).disabled){e({});return}const r=t.toUrl(a+".css");f(a,r,()=>{e({})},o=>{typeof e.error=="function"&&e.error("Could not find "+r+".")})}function f(a,t,e,i){if(h(a,t)){e();return}n(a,t,e,i)}function h(a,t){const e=window.document.getElementsByTagName("link");for(let i=0,s=e.length;i<s;i++){const r=e[i].getAttribute("data-name"),o=e[i].getAttribute("href");if(r===a||o===t)return!0}return!1}function n(a,t,e,i){const s=document.createElement("link");s.setAttribute("rel","stylesheet"),s.setAttribute("type","text/css"),s.setAttribute("data-name",a),c(a,s,e,i),s.setAttribute("href",t),(window.document.head||window.document.getElementsByTagName("head")[0]).appendChild(s)}function c(a,t,e,i){const s=()=>{t.removeEventListener("load",r),t.removeEventListener("error",o)},r=u=>{s(),e()},o=u=>{s(),i(u)};t.addEventListener("load",r),t.addEventListener("error",o)}}),function(){const d=require.getConfig().baseUrl||"https://main.vscode-cdn.net/insider/336db9ece67f682159078ea1b54212de7636d88a/out";require.config({baseUrl:d,recordStats:!0,paths:{"@microsoft/1ds-core-js":"../node_modules/@microsoft/1ds-core-js/dist/ms.core.min.js","@microsoft/1ds-post-js":"../node_modules/@microsoft/1ds-post-js/dist/ms.post.min.js","@vscode/iconv-lite-umd":"../node_modules/@vscode/iconv-lite-umd/lib/iconv-lite-umd.js","@vscode/tree-sitter-wasm":"../node_modules/@vscode/tree-sitter-wasm/wasm/tree-sitter.js","@vscode/vscode-languagedetection":"../node_modules/@vscode/vscode-languagedetection/dist/lib/index.js","@xterm/addon-clipboard":"../node_modules/@xterm/addon-clipboard/lib/addon-clipboard.js","@xterm/addon-image":"../node_modules/@xterm/addon-image/lib/addon-image.js","@xterm/addon-search":"../node_modules/@xterm/addon-search/lib/addon-search.js","@xterm/addon-serialize":"../node_modules/@xterm/addon-serialize/lib/addon-serialize.js","@xterm/addon-unicode11":"../node_modules/@xterm/addon-unicode11/lib/addon-unicode11.js","@xterm/addon-webgl":"../node_modules/@xterm/addon-webgl/lib/addon-webgl.js","@xterm/xterm":"../node_modules/@xterm/xterm/lib/xterm.js",jschardet:"../node_modules/jschardet/dist/jschardet.min.js","tas-client-umd":"../node_modules/tas-client-umd/lib/tas-client-umd.js","vscode-oniguruma":"../node_modules/vscode-oniguruma/release/main.js","vscode-textmate":"../node_modules/vscode-textmate/release/main.js","vscode-regexp-languagedetection":"../node_modules/vscode-regexp-languagedetection/dist/index.js",vsda:"../node_modules/vsda/rust/web/vsda.js","@microsoft/dynamicproto-js":"../node_modules/@microsoft/dynamicproto-js/lib/dist/umd/dynamicproto-js.min.js","@microsoft/applicationinsights-shims":"../node_modules/@microsoft/applicationinsights-shims/dist/umd/applicationinsights-shims.min.js","@microsoft/applicationinsights-core-js":"../node_modules/@microsoft/applicationinsights-core-js/browser/applicationinsights-core-js.min.js"}})}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/336db9ece67f682159078ea1b54212de7636d88a/core/vs/loader.js.map
