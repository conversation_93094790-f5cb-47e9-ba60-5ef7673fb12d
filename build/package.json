{"name": "code-oss-dev-build", "version": "1.0.0", "license": "MIT", "devDependencies": {"@azure/core-auth": "^1.9.0", "@azure/cosmos": "^3", "@azure/identity": "^4.2.1", "@azure/msal-node": "^2.16.1", "@azure/storage-blob": "^12.25.0", "@electron/get": "^2.0.0", "@types/ansi-colors": "^3.2.0", "@types/byline": "^4.2.32", "@types/debounce": "^1.0.0", "@types/debug": "^4.1.5", "@types/fancy-log": "^1.3.0", "@types/fs-extra": "^9.0.12", "@types/glob": "^7.1.1", "@types/gulp": "^4.0.17", "@types/gulp-filter": "^3.0.32", "@types/gulp-gzip": "^0.0.31", "@types/gulp-json-editor": "^2.2.31", "@types/gulp-rename": "^0.0.33", "@types/gulp-sort": "^2.0.4", "@types/gulp-sourcemaps": "^0.0.32", "@types/jws": "^3.2.10", "@types/mime": "0.0.29", "@types/minimatch": "^3.0.3", "@types/minimist": "^1.2.1", "@types/mocha": "^9.1.1", "@types/node": "22.x", "@types/pump": "^1.0.1", "@types/rimraf": "^2.0.4", "@types/through": "^0.0.29", "@types/through2": "^2.0.36", "@types/workerpool": "^6.4.0", "@types/xml2js": "0.0.33", "@vscode/iconv-lite-umd": "0.7.0", "@vscode/ripgrep": "^1.15.13", "@vscode/vsce": "2.20.1", "ansi-colors": "^3.2.3", "byline": "^5.0.0", "debug": "^4.3.2", "electron-osx-sign": "^0.4.16", "esbuild": "0.25.5", "extract-zip": "^2.0.1", "gulp-merge-json": "^2.1.1", "gulp-sort": "^2.0.0", "jsonc-parser": "^2.3.0", "jws": "^4.0.0", "mime": "^1.4.1", "source-map": "0.6.1", "ternary-stream": "^3.0.0", "through2": "^4.0.2", "tree-sitter": "^0.22.4", "vscode-universal-bundler": "^0.1.3", "workerpool": "^6.4.0", "yauzl": "^2.10.0"}, "type": "commonjs", "scripts": {"compile": "../node_modules/.bin/tsc -p tsconfig.build.json", "watch": "../node_modules/.bin/tsc -p tsconfig.build.json --watch", "npmCheckJs": "../node_modules/.bin/tsc --noEmit"}, "optionalDependencies": {"tree-sitter-typescript": "^0.23.2", "vscode-gulp-watch": "^5.0.3"}}