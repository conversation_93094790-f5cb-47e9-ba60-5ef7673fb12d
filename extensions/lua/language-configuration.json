{"comments": {"lineComment": "--", "blockComment": ["--[[", "]]"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [["{", "}"], ["[", "]"], ["(", ")"], {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"]], "indentationRules": {"increaseIndentPattern": "^((?!(\\-\\-)).)*((\\b(else|function|then|do|repeat)\\b((?!\\b(end|until)\\b).)*)|(\\{\\s*))$", "decreaseIndentPattern": "^\\s*((\\b(elseif|else|end|until)\\b)|(\\})|(\\)))"}}