{"registrations": [{"component": {"type": "git", "git": {"name": "textmate/git.tmbundle", "repositoryUrl": "https://github.com/textmate/git.tmbundle", "commitHash": "5870cf3f8abad3a6637bdf69250b5d2ded427dc4"}}, "licenseDetail": ["Copyright (c) 2008 <PERSON>", "", "Permission is hereby granted, free of charge, to any person obtaining", "a copy of this software and associated documentation files (the\"", "Software\"), to deal in the Software without restriction, including", "without limitation the rights to use, copy, modify, merge, publish,", "distribute, sublicense, and/or sell copies of the Software, and to", "permit persons to whom the Software is furnished to do so, subject to", "the following conditions:", "", "The above copyright notice and this permission notice shall be", "included in all copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,", "EXPRESS OR <PERSON><PERSON><PERSON><PERSON><PERSON>, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF", "MERCHANTABILITY, FITNE<PERSON> FOR A PARTICULAR PURPOSE AND", "NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE", "LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION", "OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION", "WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."], "license": "MIT", "version": "0.0.0"}, {"component": {"type": "git", "git": {"name": "walles/git-commit-message-plus", "repositoryUrl": "https://github.com/walles/git-commit-message-plus", "commitHash": "35a079dea5a91b087021b40c01a6bb4eb0337a87"}}, "licenseDetail": ["Copyright (c) 2023 <PERSON> <<EMAIL>>", "", "Permission is hereby granted, free of charge, to any person obtaining", "a copy of this software and associated documentation files (the\"", "Software\"), to deal in the Software without restriction, including", "without limitation the rights to use, copy, modify, merge, publish,", "distribute, sublicense, and/or sell copies of the Software, and to", "permit persons to whom the Software is furnished to do so, subject to", "the following conditions:", "", "The above copyright notice and this permission notice shall be", "included in all copies or substantial portions of the Software.", "", "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,", "EXPRESS OR <PERSON><PERSON><PERSON><PERSON><PERSON>, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF", "MERCHANTABILITY, FITNE<PERSON> FOR A PARTICULAR PURPOSE AND", "NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE", "LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION", "OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION", "WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."], "license": "MIT", "version": "1.0.0", "description": "The original JSON grammars were derived from https://github.com/microsoft/vscode/blob/e95c74c4c7af876e79ec58df262464467c06df28/extensions/git-base/syntaxes/git-commit.tmLanguage.json. That file was originally copied from https://github.com/textmate/git.tmbundle."}], "version": 1}