{"version": "0.2.0", "configurations": [{"name": "Launch Extension", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "stopOnEntry": false, "sourceMaps": true, "outFiles": ["${workspaceFolder}/client/out"]}, {"name": "Launch Tests", "type": "extensionHost", "request": "launch", "runtimeExecutable": "${execPath}", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/client/out/test"], "stopOnEntry": false, "sourceMaps": true, "outFiles": ["${workspaceFolder}/client/out/test"]}, {"name": "Attach Language Server", "type": "node", "request": "attach", "port": 6004, "sourceMaps": true, "outFiles": ["${workspaceFolder}/server/out"]}], "compounds": [{"name": "Launch Extension and Attach Language Server", "configurations": ["Launch Extension", "Attach Language Server"]}]}