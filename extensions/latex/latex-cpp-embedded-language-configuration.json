{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [{"open": "[", "close": "]"}, {"open": "{", "close": "}"}, {"open": "(", "close": ")"}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}, {"open": "\"", "close": "\"", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["\"", "\""], ["'", "'"], ["<", ">"]], "wordPattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)", "folding": {"markers": {"start": "^\\s*#pragma\\s+region\\b", "end": "^\\s*#pragma\\s+endregion\\b"}}}