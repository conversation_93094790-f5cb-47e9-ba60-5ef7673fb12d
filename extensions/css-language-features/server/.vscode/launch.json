{
	"version": "0.1.0",
	// List of configurations. Add new configurations or edit existing ones.
	"configurations": [
		{
			"name": "Attach",
			"type": "node",
			"request": "attach",
			"port": 6044,
			"protocol": "inspector",
			"sourceMaps": true,
			"outFiles": ["${workspaceFolder}/out/**/*.js"]
		},
		{
			"name": "Unit Tests",
			"type": "node",
			"request": "launch",
			"program": "${workspaceFolder}/../../../node_modules/mocha/bin/_mocha",
			"stopOnEntry": false,
			"args": [
				"--timeout",
				"999999",
				"--colors"
			],
			"cwd": "${workspaceFolder}",
			"runtimeExecutable": null,
			"runtimeArgs": [],
			"env": {},
			"sourceMaps": true,
			"outFiles": ["${workspaceFolder}/out/**/*.js"]
		}
	]
}