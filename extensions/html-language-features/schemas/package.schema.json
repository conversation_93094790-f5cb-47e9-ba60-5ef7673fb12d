{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"contributes": {"type": "object", "properties": {"html.customData": {"type": "array", "markdownDescription": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md).\n\nVS Code loads custom data on startup to enhance its HTML support for the custom HTML tags, attributes and attribute values you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.", "items": {"type": "string", "description": "Relative path to a HTML custom data file"}}, "htmlLanguageParticipants": {"type": "array", "description": "A list of languages that participate with the HTML language server.", "items": {"type": "object", "properties": {"languageId": {"type": "string", "description": "The id of the language that participates with HTML language server."}, "autoInsert": {"type": "boolean", "description": "Whether the language participates with HTML auto insertions. If not specified, defaults to <code>true</code>."}}}}}}}}